{"format": 1, "restore": {"C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopRepositories\\ShopRepositories.csproj": {}}, "projects": {"C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopEntities\\ShopEntities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopEntities\\ShopEntities.csproj", "projectName": "ShopEntities", "projectPath": "C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopEntities\\ShopEntities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopEntities\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.401\\RuntimeIdentifierGraph.json"}}}, "C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopRepositories\\ShopRepositories.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopRepositories\\ShopRepositories.csproj", "projectName": "ShopRepositories", "projectPath": "C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopRepositories\\ShopRepositories.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopRepositories\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopEntities\\ShopEntities.csproj": {"projectPath": "C:\\myaz203205\\Practice\\Week10\\ShoppingPlatform\\ShopEntities\\ShopEntities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.401\\RuntimeIdentifierGraph.json"}}}}}